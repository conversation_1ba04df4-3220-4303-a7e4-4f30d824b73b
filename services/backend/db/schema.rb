# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.0].define(version: 2025_04_07_105307) do
  create_table "agencies", charset: "utf8mb4", collation: "utf8mb4_0900_bin", force: :cascade do |t|
    t.string "name", null: false
    t.datetime "deleted_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["deleted_at"], name: "index_agencies_on_deleted_at"
  end

  create_table "article_reads", charset: "utf8mb4", collation: "utf8mb4_0900_bin", force: :cascade do |t|
    t.bigint "article_id", null: false
    t.bigint "creator_id", null: false
    t.datetime "viewed_at", default: -> { "(now())" }, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["article_id", "creator_id"], name: "index_article_reads_on_article_id_and_creator_id", unique: true
    t.index ["article_id"], name: "index_article_reads_on_article_id"
    t.index ["creator_id"], name: "index_article_reads_on_creator_id"
  end

  create_table "articles", charset: "utf8mb4", collation: "utf8mb4_0900_bin", force: :cascade do |t|
    t.string "title", null: false
    t.text "description"
    t.string "image_url"
    t.integer "label", default: 0, null: false
    t.integer "priority", default: 0, null: false
    t.text "content"
    t.datetime "published_at"
    t.datetime "expire_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["label"], name: "index_articles_on_label"
    t.index ["published_at"], name: "index_articles_on_published_at"
  end

  create_table "audit_groups", id: { type: :bigint, unsigned: true }, charset: "utf8mb4", collation: "utf8mb4_0900_bin", force: :cascade do |t|
    t.string "user_id", limit: 50, null: false
    t.string "audit_type", limit: 50, null: false, comment: "種別（SHOP,SHOP_ITEM,FANME_PROFILE,FANME_CONTENT,など）"
    t.string "operation_type", limit: 50, null: false, comment: "INSERT,UPDATE,DELETE"
    t.text "metadata", comment: "json形式（shop_id,item_id,title,description,など動的に入る）"
    t.integer "status", null: false, comment: "0:未監査, -1:却下, 1:PENDING, 5:再提出, 9:承認"
    t.text "comment", comment: "監査コメント"
    t.datetime "audited_at", precision: nil, comment: "監査実施日時"
    t.string "audited_user_id", limit: 50
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil
    t.index ["audit_type"], name: "idx_audit_groups_audit_type"
    t.index ["status"], name: "idx_audit_groups_status"
    t.index ["user_id"], name: "idx_audit_groups_user_id"
  end

  create_table "audit_objects", id: { type: :bigint, unsigned: true }, charset: "utf8mb4", collation: "utf8mb4_0900_bin", force: :cascade do |t|
    t.bigint "audit_group_id", null: false
    t.string "bucket", limit: 50, null: false
    t.string "file_path", null: false
    t.string "asset_type", limit: 50, null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil
    t.index ["audit_group_id"], name: "idx_audit_group_id"
  end

  create_table "blogs", charset: "utf8mb4", collation: "utf8mb4_0900_bin", force: :cascade do |t|
    t.string "category", comment: "記事の種類（fanme_apps）"
    t.text "image_url"
    t.text "page_url"
    t.text "description"
    t.integer "order", null: false, comment: "表示順"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["category", "order"], name: "idx_type_order_uindex", unique: true
  end

  create_table "campaign_entries", charset: "utf8mb4", collation: "utf8mb4_0900_bin", force: :cascade do |t|
    t.bigint "campaign_id", null: false
    t.bigint "user_id", null: false
    t.datetime "entered_at", precision: nil, default: -> { "CURRENT_TIMESTAMP" }, null: false
    t.datetime "created_at", precision: nil, default: -> { "CURRENT_TIMESTAMP" }, null: false
    t.datetime "updated_at", precision: nil, default: -> { "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP" }, null: false
    t.index ["campaign_id", "user_id"], name: "unique_campaign_creator", unique: true
    t.index ["campaign_id"], name: "idx_campaign_id"
    t.index ["user_id"], name: "idx_user_id"
  end

  create_table "campaigns", charset: "utf8mb4", collation: "utf8mb4_0900_bin", force: :cascade do |t|
    t.string "campaign_identity", null: false
    t.string "title", null: false
    t.bigint "user_id"
    t.string "entry_type", limit: 50, null: false
    t.string "action_type", limit: 50, null: false
    t.integer "action_duration_days", default: 0, null: false
    t.datetime "start_at", precision: nil, null: false
    t.datetime "end_at", precision: nil, null: false
    t.datetime "created_at", precision: nil, default: -> { "CURRENT_TIMESTAMP" }, null: false
    t.datetime "updated_at", precision: nil, default: -> { "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP" }, null: false
    t.index ["campaign_identity"], name: "campaign_identity", unique: true
  end

  create_table "console_users", charset: "utf8mb4", collation: "utf8mb4_0900_bin", force: :cascade do |t|
    t.bigint "creator_id", null: false
    t.bigint "agency_id"
    t.string "role", null: false
    t.datetime "deleted_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["agency_id"], name: "index_console_users_on_agency_id"
    t.index ["creator_id"], name: "index_console_users_on_creator_id"
    t.index ["creator_id"], name: "unique_console_users_creator_id", unique: true
    t.index ["deleted_at"], name: "index_console_users_on_deleted_at"
  end

  create_table "content_block_details", charset: "utf8mb4", collation: "utf8mb4_0900_bin", force: :cascade do |t|
    t.string "icon", default: ""
    t.string "title", default: "", null: false
    t.text "description"
    t.text "app_description"
    t.text "url"
    t.json "style"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "content_block_groups", charset: "utf8mb4", collation: "utf8mb4_0900_bin", force: :cascade do |t|
    t.bigint "content_block_id", null: false
    t.bigint "content_block_detail_id", null: false
    t.integer "content_group_number", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["content_block_detail_id"], name: "content_block_groups_on_content_block_detail_id", unique: true
    t.index ["content_block_detail_id"], name: "index_content_block_groups_on_content_block_detail_id"
    t.index ["content_block_id", "content_group_number"], name: "cb_groups_on_content_block_id_and_content_group_number", unique: true
    t.index ["content_block_id"], name: "index_content_block_groups_on_content_block_id"
  end

  create_table "content_block_types", charset: "utf8mb4", collation: "utf8mb4_0900_bin", force: :cascade do |t|
    t.string "name", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["name"], name: "index_content_block_types_on_name", unique: true
  end

  create_table "content_blocks", charset: "utf8mb4", collation: "utf8mb4_0900_bin", force: :cascade do |t|
    t.bigint "creator_id", null: false
    t.bigint "content_block_type_id", null: false
    t.integer "display_order_number", null: false
    t.boolean "displayable", default: true, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["content_block_type_id"], name: "index_content_blocks_on_content_block_type_id"
    t.index ["creator_id", "display_order_number"], name: "index_content_blocks_on_creator_id_and_display_order_number", unique: true
    t.index ["creator_id"], name: "index_content_blocks_on_creator_id"
  end

  create_table "creator_popups", charset: "utf8mb4", collation: "utf8mb4_0900_bin", force: :cascade do |t|
    t.bigint "creator_id", null: false
    t.boolean "enable", default: false, null: false
    t.string "title"
    t.string "url", null: false
    t.string "button_text"
    t.string "image", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["creator_id"], name: "index_creator_popups_on_creator_id"
  end

  create_table "creator_states", charset: "utf8mb4", collation: "utf8mb4_0900_bin", force: :cascade do |t|
    t.bigint "creator_id", unsigned: true
    t.string "key", null: false
    t.string "value"
    t.text "data"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["creator_id", "key"], name: "index_creator_states_on_creator_id_and_key"
    t.index ["key", "value"], name: "index_creator_states_on_key_and_value"
  end

  create_table "creator_tokens", charset: "utf8mb4", collation: "utf8mb4_0900_bin", force: :cascade do |t|
    t.bigint "creator_id", null: false
    t.text "id_token"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["creator_id"], name: "index_creator_tokens_on_creator_id", unique: true
  end

  create_table "creator_withdrawals", charset: "utf8mb4", collation: "utf8mb4_0900_bin", force: :cascade do |t|
    t.bigint "creator_id", null: false
    t.integer "reason", null: false
    t.text "detail"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["creator_id"], name: "index_creator_withdrawals_on_creator_id"
  end

  create_table "creators", charset: "utf8mb4", collation: "utf8mb4_0900_bin", force: :cascade do |t|
    t.string "icon"
    t.string "name", null: false
    t.string "gender", default: "BLANK", null: false
    t.date "birthday", default: -> { "(curdate())" }, null: false
    t.boolean "birthday_confirmed", default: false, null: false
    t.integer "is_birthday_week", default: 0, null: false
    t.string "account_identity", null: false
    t.boolean "is_public", default: true, null: false
    t.boolean "allow_public_sharing", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "uid"
    t.datetime "deleted_at"
    t.boolean "filled_profile", default: true, null: false
    t.integer "purpose", default: 0, null: false
    t.datetime "last_accessed_at"
    t.index ["account_identity"], name: "index_creators_on_account_identity", unique: true
    t.index ["deleted_at"], name: "index_creators_on_deleted_at"
    t.index ["purpose"], name: "index_creators_on_purpose"
    t.index ["uid"], name: "index_creators_on_uid", unique: true
  end

  create_table "displayable_cover_images", charset: "utf8mb4", collation: "utf8mb4_0900_bin", force: :cascade do |t|
    t.bigint "profile_cover_id", null: false
    t.bigint "profile_cover_image_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["profile_cover_id"], name: "index_displayable_cover_images_on_profile_cover_id"
    t.index ["profile_cover_image_id"], name: "index_displayable_cover_images_on_profile_cover_image_id"
  end

  create_table "docs", charset: "utf8mb4", collation: "utf8mb4_0900_bin", force: :cascade do |t|
    t.string "name"
    t.string "slug", null: false
    t.string "version", null: false
    t.text "description"
    t.text "content", size: :long
    t.datetime "start_date", null: false
    t.integer "remind_before_date"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["slug", "version"], name: "index_docs_on_slug_and_version", unique: true
    t.index ["start_date"], name: "index_docs_on_start_date"
  end

  create_table "fcm_tokens", charset: "utf8mb4", collation: "utf8mb4_0900_bin", force: :cascade do |t|
    t.string "token", null: false
    t.string "creator_uid", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["creator_uid"], name: "index_fcm_tokens_on_creator_uid"
    t.index ["token"], name: "index_fcm_tokens_on_token", unique: true
  end

  create_table "flyway_schema_history", primary_key: "installed_rank", id: :integer, default: nil, charset: "utf8mb4", collation: "utf8mb4_0900_bin", force: :cascade do |t|
    t.string "version", limit: 50
    t.string "description", limit: 200, null: false
    t.string "type", limit: 20, null: false
    t.string "script", limit: 1000, null: false
    t.integer "checksum"
    t.string "installed_by", limit: 100, null: false
    t.timestamp "installed_on", default: -> { "CURRENT_TIMESTAMP" }, null: false
    t.integer "execution_time", null: false
    t.boolean "success", null: false
    t.index ["success"], name: "flyway_schema_history_s_idx"
  end

  create_table "general_event_users", charset: "utf8mb4", collation: "utf8mb4_0900_bin", force: :cascade do |t|
    t.bigint "general_event_id", null: false
    t.bigint "user_id", null: false
    t.integer "rank", default: 0
    t.integer "yell_count", default: 0
    t.datetime "aggregation_at"
    t.integer "previous_rank", default: 0
    t.integer "previous_yell_count", default: 0
    t.datetime "previous_aggregation_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["aggregation_at"], name: "index_general_event_users_on_aggregation_at"
    t.index ["general_event_id"], name: "index_general_event_users_on_general_event_id"
    t.index ["previous_aggregation_at"], name: "index_general_event_users_on_previous_aggregation_at"
    t.index ["user_id"], name: "index_general_event_users_on_user_id"
  end

  create_table "general_events", charset: "utf8mb4", collation: "utf8mb4_0900_bin", force: :cascade do |t|
    t.bigint "creator_id", null: false
    t.integer "event_type", default: 0, null: false
    t.datetime "prepare_at", null: false
    t.datetime "start_at", null: false
    t.datetime "end_at", null: false
    t.datetime "archived_at", null: false
    t.datetime "completed_at", comment: "設定されたら開始済"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["archived_at"], name: "index_general_events_on_archived_at"
    t.index ["creator_id"], name: "index_general_events_on_creator_id"
    t.index ["end_at"], name: "index_general_events_on_end_at"
    t.index ["prepare_at"], name: "index_general_events_on_prepare_at"
    t.index ["start_at"], name: "index_general_events_on_start_at"
  end

  create_table "lp_banners", charset: "utf8mb4", collation: "utf8mb4_0900_bin", force: :cascade do |t|
    t.text "navigation_url", null: false
    t.text "image_url", null: false
    t.integer "order", null: false
    t.string "alt", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "lp_case_studies", charset: "utf8mb4", collation: "utf8mb4_0900_bin", force: :cascade do |t|
    t.text "navigation_url", null: false
    t.text "image_url", null: false
    t.integer "order", null: false
    t.string "alt", null: false
    t.string "name", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "notification_acceptances", charset: "utf8mb4", collation: "utf8mb4_0900_bin", force: :cascade do |t|
    t.bigint "creator_id", null: false
    t.string "category", null: false
    t.boolean "accept_flg", default: true, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["category", "accept_flg"], name: "index_notification_acceptances_on_category_and_accept_flg"
    t.index ["creator_id", "category"], name: "index_notification_acceptances_on_creator_id_and_category", unique: true
    t.index ["creator_id"], name: "index_notification_acceptances_on_creator_id"
  end

  create_table "notification_settings", charset: "utf8mb4", collation: "utf8mb4_0900_bin", force: :cascade do |t|
    t.boolean "enabled", default: false, null: false
    t.string "creator_uid", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["creator_uid"], name: "index_notification_settings_on_creator_uid"
  end

  create_table "offline_event_creators", charset: "utf8mb4", collation: "utf8mb4_0900_bin", force: :cascade do |t|
    t.bigint "offline_event_id"
    t.bigint "creator_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["creator_id"], name: "index_offline_event_creators_on_creator_id"
    t.index ["offline_event_id", "creator_id"], name: "index_offline_event_creators_on_offline_event_id_and_creator_id", unique: true
    t.index ["offline_event_id"], name: "index_offline_event_creators_on_offline_event_id"
  end

  create_table "offline_events", charset: "utf8mb4", collation: "utf8mb4_0900_bin", force: :cascade do |t|
    t.string "name", null: false
    t.text "description"
    t.datetime "start_at", null: false
    t.datetime "end_at", null: false
    t.string "image_url"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "profile_cover_images", charset: "utf8mb4", collation: "utf8mb4_0900_bin", force: :cascade do |t|
    t.bigint "profile_cover_id", null: false
    t.string "resource", null: false
    t.string "resource_type", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["profile_cover_id"], name: "index_profile_cover_images_on_profile_cover_id"
  end

  create_table "profile_covers", charset: "utf8mb4", collation: "utf8mb4_0900_bin", force: :cascade do |t|
    t.bigint "profile_id", null: false
    t.string "brightness", null: false
    t.boolean "cover_visibility", default: true, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["profile_id"], name: "index_profile_covers_on_profile_id"
  end

  create_table "profile_theme_colors", charset: "utf8mb4", collation: "utf8mb4_0900_bin", force: :cascade do |t|
    t.bigint "profile_id", null: false
    t.bigint "theme_color_id", null: false
    t.string "custom_color"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["profile_id"], name: "index_profile_theme_colors_on_profile_id"
    t.index ["theme_color_id"], name: "index_profile_theme_colors_on_theme_color_id"
  end

  create_table "profiles", charset: "utf8mb4", collation: "utf8mb4_0900_bin", force: :cascade do |t|
    t.bigint "creator_id", null: false
    t.text "bio", null: false
    t.string "header_image"
    t.string "sns_link_color", default: "ORIG", null: false
    t.boolean "official_flg", default: false, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["creator_id"], name: "index_profiles_on_creator_id"
  end

  create_table "ranking_event_creators", charset: "utf8mb4", collation: "utf8mb4_0900_bin", force: :cascade do |t|
    t.bigint "ranking_event_id", null: false
    t.bigint "creator_id", null: false
    t.text "comment", null: false, comment: "参加表明コメント"
    t.integer "status", default: 0, null: false, comment: "0=参加申請, 9=参加中, -1=参加拒否"
    t.boolean "is_first_participation", default: false, null: false
    t.integer "rank"
    t.integer "yell_count"
    t.datetime "aggregation_at"
    t.integer "previous_rank"
    t.integer "previous_yell_count"
    t.datetime "previous_aggregation_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["creator_id", "status"], name: "index_creator_status"
    t.index ["creator_id"], name: "index_ranking_event_creators_on_creator_id"
    t.index ["ranking_event_id", "creator_id"], name: "index_ranking_creator", unique: true
    t.index ["ranking_event_id"], name: "index_ranking_event_creators_on_ranking_event_id"
  end

  create_table "ranking_event_users", charset: "utf8mb4", collation: "utf8mb4_0900_bin", force: :cascade do |t|
    t.bigint "ranking_event_creator_id", null: false
    t.bigint "user_id", null: false
    t.integer "rank", default: 0
    t.integer "yell_count", default: 0
    t.datetime "aggregation_at"
    t.integer "previous_rank", default: 0
    t.integer "previous_yell_count", default: 0
    t.datetime "previous_aggregation_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["aggregation_at"], name: "index_ranking_event_users_on_aggregation_at"
    t.index ["previous_aggregation_at"], name: "index_ranking_event_users_on_previous_aggregation_at"
    t.index ["ranking_event_creator_id"], name: "index_ranking_event_users_on_ranking_event_creator_id"
    t.index ["user_id"], name: "index_ranking_event_users_on_user_id"
  end

  create_table "ranking_events", charset: "utf8mb4", collation: "utf8mb4_0900_bin", force: :cascade do |t|
    t.string "event_identity", null: false
    t.string "name", null: false
    t.text "description", null: false
    t.string "image_url", null: false
    t.string "base_color", default: "#B53599", null: false
    t.json "add_infos", comment: "LPの追加情報群"
    t.string "judge_x", comment: "審査員のX URL"
    t.string "judge_instagram", comment: "審査員のInstagram URL"
    t.string "share_hashtags", comment: "シェアハッシュタグ（カンマ区切り）"
    t.string "results", comment: "結果順位一覧"
    t.datetime "apply_start_at", null: false
    t.datetime "apply_end_at", null: false
    t.datetime "start_at", null: false
    t.datetime "end_at", null: false
    t.datetime "calculated_at", null: false
    t.datetime "archived_at", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["apply_end_at"], name: "index_ranking_events_on_apply_end_at"
    t.index ["apply_start_at"], name: "index_ranking_events_on_apply_start_at"
    t.index ["archived_at"], name: "index_ranking_events_on_archived_at"
    t.index ["calculated_at"], name: "index_ranking_events_on_calculated_at"
    t.index ["end_at"], name: "index_ranking_events_on_end_at"
    t.index ["event_identity"], name: "index_ranking_events_on_event_identity"
    t.index ["name"], name: "index_ranking_events_on_name"
    t.index ["start_at"], name: "index_ranking_events_on_start_at"
  end

  create_table "ranking_fan_badges", charset: "utf8mb4", collation: "utf8mb4_0900_bin", force: :cascade do |t|
    t.bigint "ranking_event_id", null: false
    t.integer "rank", default: 0, null: false
    t.text "badge_url", null: false
    t.text "badge_top_url"
    t.text "image_url", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["ranking_event_id"], name: "index_ranking_fan_badges_on_ranking_event_id"
  end

  create_table "ranking_yell_boosts", charset: "utf8mb4", collation: "utf8mb4_0900_bin", force: :cascade do |t|
    t.bigint "ranking_event_id"
    t.datetime "start_at", null: false
    t.datetime "end_at", null: false
    t.decimal "boost_ratio", precision: 10, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["end_at"], name: "index_ranking_yell_boosts_on_end_at"
    t.index ["ranking_event_id"], name: "index_ranking_yell_boosts_on_ranking_event_id"
    t.index ["start_at"], name: "index_ranking_yell_boosts_on_start_at"
  end

  create_table "restricted_accounts", charset: "utf8mb4", collation: "utf8mb4_0900_bin", force: :cascade do |t|
    t.bigint "creator_id", null: false
    t.integer "restrict_type", default: 0, null: false
    t.datetime "start_at"
    t.datetime "end_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["creator_id"], name: "index_restricted_accounts_on_creator_id"
  end

  create_table "shortened_urls", charset: "utf8mb4", collation: "utf8mb4_0900_bin", force: :cascade do |t|
    t.bigint "owner_id"
    t.string "owner_type", limit: 20
    t.text "url", null: false
    t.string "unique_key", limit: 10, null: false
    t.integer "use_count", default: 0, null: false
    t.datetime "expires_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["owner_id", "owner_type"], name: "index_shortened_urls_on_owner_id_and_owner_type"
    t.index ["unique_key"], name: "index_shortened_urls_on_unique_key", unique: true
    t.index ["url"], name: "index_shortened_urls_on_url", length: 767
  end

  create_table "sns_link_displays", charset: "utf8mb4", collation: "utf8mb4_0900_bin", force: :cascade do |t|
    t.bigint "profile_id", null: false
    t.bigint "sns_link_id", null: false
    t.integer "display_order_number", null: false
    t.boolean "displayable", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["profile_id", "display_order_number"], name: "index_sns_link_displays_on_profile_id_and_display_order_number", unique: true
    t.index ["profile_id"], name: "index_sns_link_displays_on_profile_id"
    t.index ["sns_link_id"], name: "index_sns_link_displays_on_sns_link_id"
  end

  create_table "sns_links", charset: "utf8mb4", collation: "utf8mb4_0900_bin", force: :cascade do |t|
    t.bigint "profile_id", null: false
    t.string "type", null: false
    t.string "account_identity", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["profile_id", "type"], name: "index_sns_links_on_profile_id_and_type", unique: true
    t.index ["profile_id"], name: "index_sns_links_on_profile_id"
  end

  create_table "theme_colors", charset: "utf8mb4", collation: "utf8mb4_0900_bin", force: :cascade do |t|
    t.string "color", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "user_general_event_fan_badges", charset: "utf8mb4", collation: "utf8mb4_0900_bin", force: :cascade do |t|
    t.bigint "general_event_id", null: false
    t.integer "rank", default: 0, null: false
    t.bigint "user_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["general_event_id"], name: "index_user_general_event_fan_badges_on_general_event_id"
    t.index ["user_id"], name: "index_user_general_event_fan_badges_on_user_id"
  end

  create_table "user_general_event_yell_logs", charset: "utf8mb4", collation: "utf8mb4_0900_bin", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "target_creator_id", null: false
    t.integer "log_type", default: 0, null: false, comment: "0=クリック, 1=購入, 2=シェア"
    t.integer "yell_count", default: 1, null: false
    t.bigint "transaction_id"
    t.datetime "action_at", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["log_type"], name: "index_user_general_event_yell_logs_on_log_type"
    t.index ["target_creator_id", "action_at"], name: "index_user_action_at"
    t.index ["target_creator_id"], name: "index_user_general_event_yell_logs_on_target_creator_id"
    t.index ["transaction_id"], name: "index_user_general_event_yell_logs_on_transaction_id"
    t.index ["user_id", "action_at"], name: "index_creator_action_at"
    t.index ["user_id"], name: "index_user_general_event_yell_logs_on_user_id"
  end

  create_table "user_ranking_fan_badges", charset: "utf8mb4", collation: "utf8mb4_0900_bin", force: :cascade do |t|
    t.bigint "ranking_fan_badge_id", null: false
    t.bigint "creator_id", null: false
    t.bigint "user_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["creator_id"], name: "index_user_ranking_fan_badges_on_creator_id"
    t.index ["ranking_fan_badge_id"], name: "index_user_ranking_fan_badges_on_ranking_fan_badge_id"
    t.index ["user_id"], name: "index_user_ranking_fan_badges_on_user_id"
  end

  create_table "user_tutorials", charset: "utf8mb4", collation: "utf8mb4_0900_bin", force: :cascade do |t|
    t.bigint "creator_id", null: false
    t.string "name", null: false
    t.boolean "display_flg", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["creator_id", "name"], name: "index_user_tutorials_on_creator_id_and_name", unique: true
    t.index ["creator_id"], name: "index_user_tutorials_on_creator_id"
  end

  create_table "user_yell_logs", charset: "utf8mb4", collation: "utf8mb4_0900_bin", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "target_creator_id", null: false
    t.integer "log_type", default: 0, null: false, comment: "0=クリック, 1=購入, 2=シェア"
    t.integer "yell_count", default: 1, null: false
    t.bigint "transaction_id"
    t.datetime "action_at", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["log_type"], name: "index_user_yell_logs_on_log_type"
    t.index ["target_creator_id", "action_at"], name: "index_user_action_at"
    t.index ["target_creator_id", "user_id", "action_at"], name: "index_creator_user_action_at"
    t.index ["target_creator_id"], name: "index_user_yell_logs_on_target_creator_id"
    t.index ["transaction_id"], name: "index_user_yell_logs_on_transaction_id"
    t.index ["user_id", "action_at"], name: "index_creator_action_at"
    t.index ["user_id"], name: "index_user_yell_logs_on_user_id"
  end

  add_foreign_key "article_reads", "articles"
  add_foreign_key "article_reads", "creators"
  add_foreign_key "console_users", "agencies"
  add_foreign_key "console_users", "creators"
  add_foreign_key "content_block_groups", "content_block_details"
  add_foreign_key "content_block_groups", "content_blocks"
  add_foreign_key "content_blocks", "content_block_types"
  add_foreign_key "content_blocks", "creators"
  add_foreign_key "creator_popups", "creators"
  add_foreign_key "creator_tokens", "creators"
  add_foreign_key "creator_withdrawals", "creators"
  add_foreign_key "displayable_cover_images", "profile_cover_images"
  add_foreign_key "displayable_cover_images", "profile_covers"
  add_foreign_key "general_event_users", "creators", column: "user_id"
  add_foreign_key "general_event_users", "general_events"
  add_foreign_key "general_events", "creators"
  add_foreign_key "notification_acceptances", "creators"
  add_foreign_key "offline_event_creators", "creators"
  add_foreign_key "offline_event_creators", "offline_events"
  add_foreign_key "profile_cover_images", "profile_covers"
  add_foreign_key "profile_covers", "profiles"
  add_foreign_key "profile_theme_colors", "profiles"
  add_foreign_key "profile_theme_colors", "theme_colors"
  add_foreign_key "profiles", "creators"
  add_foreign_key "ranking_event_creators", "creators"
  add_foreign_key "ranking_event_creators", "ranking_events"
  add_foreign_key "ranking_event_users", "creators", column: "user_id"
  add_foreign_key "ranking_event_users", "ranking_event_creators"
  add_foreign_key "ranking_fan_badges", "ranking_events"
  add_foreign_key "ranking_yell_boosts", "ranking_events"
  add_foreign_key "restricted_accounts", "creators"
  add_foreign_key "sns_link_displays", "profiles"
  add_foreign_key "sns_link_displays", "sns_links"
  add_foreign_key "sns_links", "profiles"
  add_foreign_key "user_general_event_fan_badges", "creators", column: "user_id"
  add_foreign_key "user_general_event_fan_badges", "general_events"
  add_foreign_key "user_general_event_yell_logs", "creators", column: "target_creator_id"
  add_foreign_key "user_general_event_yell_logs", "creators", column: "user_id"
  add_foreign_key "user_ranking_fan_badges", "ranking_fan_badges"
  add_foreign_key "user_tutorials", "creators"
  add_foreign_key "user_yell_logs", "creators", column: "target_creator_id"
  add_foreign_key "user_yell_logs", "creators", column: "user_id"
end
